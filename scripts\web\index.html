<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Paris Elite Services - Générateurs de Documents</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .header h1 {
            color: #2c3e50;
            font-size: 2.5rem;
            margin-bottom: 10px;
        }
        
        .header p {
            color: #666;
            font-size: 1.2rem;
        }
        
        .cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin-bottom: 30px;
        }
        
        .card {
            border: 2px solid #e0e6ed;
            border-radius: 10px;
            padding: 25px;
            text-align: center;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }
        
        .card:hover {
            border-color: #667eea;
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.2);
        }
        
        .card h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.3rem;
        }
        
        .card p {
            color: #666;
            margin-bottom: 20px;
            line-height: 1.6;
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            margin: 5px;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
        }
        
        .status {
            background: #e8f4f8;
            border: 2px solid #0066cc;
            border-radius: 10px;
            padding: 20px;
            margin-top: 30px;
            text-align: center;
        }
        
        .status h4 {
            color: #0066cc;
            margin-bottom: 15px;
        }
        
        .status-ok {
            color: #28a745;
            font-weight: bold;
        }
        
        @media (max-width: 768px) {
            .cards {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚗 PARIS ELITE SERVICES</h1>
            <p>Générateurs de Documents Officiels</p>
        </div>

        <div class="cards">
            <div class="card">
                <h3>🚗 Générateur VTC</h3>
                <p>Document rapide pour services de transport et contrôles de police. Interface moderne avec génération automatique de numéros de réservation.</p>
                <a href="generador_vtc_paris.html" class="btn">Ouvrir VTC Generator</a>
            </div>

            <div class="card">
                <h3>📋 Bon de Réservation</h3>
                <p>Document complet pour réservations touristiques avec multiples services. Idéal pour contrôles fronterizos et justificatifs officiels.</p>
                <a href="../plantillas/bon_reservation.html" class="btn">Ouvrir Bon de Réservation</a>
            </div>
        </div>

        <div class="status">
            <h4>📊 Status du Testing</h4>
            <p>✅ <span class="status-ok">Serveur Local Actif</span></p>
            <p>✅ <span class="status-ok">Générateurs Fonctionnels</span></p>
            <p>✅ <span class="status-ok">Prêt pour Testing</span></p>
        </div>

        <div style="text-align: center; margin-top: 30px; padding: 20px; background: #fff3cd; border-radius: 10px;">
            <h4 style="color: #856404; margin-bottom: 15px;">📋 Instructions de Testing</h4>
            <ol style="color: #856404; text-align: left; max-width: 500px; margin: 0 auto;">
                <li>Cliquez sur un générateur ci-dessus</li>
                <li>Remplissez le formulaire avec des données réelles</li>
                <li>Cliquez sur "Générer le Document"</li>
                <li>Vérifiez l'aperçu du document</li>
                <li>Testez l'impression (Ctrl+P)</li>
                <li>Testez sur mobile (F12 → Mode responsive)</li>
            </ol>
        </div>
    </div>

    <script>
        // Vérification automatique des fichiers
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 Testing Environment Loaded');
            console.log('📁 Available generators:');
            console.log('   - VTC Generator: generador_vtc_paris.html');
            console.log('   - Bon de Réservation: ../plantillas/bon_reservation.html');
        });
    </script>
</body>
</html>
