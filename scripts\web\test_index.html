<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Testing - Générateurs de Documents</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
            color: #2c3e50;
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.2rem;
            color: #666;
        }
        
        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 25px;
            margin-bottom: 30px;
        }
        
        .test-card {
            border: 2px solid #e0e6ed;
            border-radius: 10px;
            padding: 25px;
            background: #f8f9fa;
            transition: all 0.3s ease;
        }
        
        .test-card:hover {
            border-color: #667eea;
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.2);
        }
        
        .test-card h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.3rem;
        }
        
        .test-card p {
            color: #666;
            margin-bottom: 20px;
            line-height: 1.6;
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            margin: 5px;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
        }
        
        .btn-secondary {
            background: linear-gradient(135deg, #28a745, #20c997);
        }
        
        .btn-warning {
            background: linear-gradient(135deg, #ffc107, #ff8c00);
        }
        
        .status-section {
            background: #e8f4f8;
            border: 2px solid #0066cc;
            border-radius: 10px;
            padding: 20px;
            margin-top: 30px;
        }
        
        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 10px 0;
            padding: 10px;
            background: white;
            border-radius: 5px;
        }
        
        .status-ok {
            color: #28a745;
            font-weight: bold;
        }
        
        .status-error {
            color: #dc3545;
            font-weight: bold;
        }
        
        .instructions {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
        }
        
        .instructions h4 {
            color: #856404;
            margin-bottom: 15px;
        }
        
        .instructions ol {
            color: #856404;
            line-height: 1.8;
        }
        
        @media (max-width: 768px) {
            .test-grid {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 Testing Environment</h1>
            <p>Générateurs de Documents - Paris Elite Services</p>
        </div>

        <div class="test-grid">
            <div class="test-card">
                <h3>🚗 Générateur VTC</h3>
                <p>Document rapide pour services VTC et contrôles de police. Interface moderne avec génération automatique de numéros.</p>
                <a href="generador_vtc_paris.html" class="btn" target="_blank">Tester VTC</a>
                <button onclick="testVTC()" class="btn btn-secondary">Test Auto</button>
            </div>

            <div class="test-card">
                <h3>📋 Bon de Réservation</h3>
                <p>Document complet pour réservations touristiques avec multiple services. Idéal pour contrôles fronterizos.</p>
                <a href="generador_bon_reservation.html" class="btn" target="_blank">Tester Réservation</a>
                <button onclick="testReservation()" class="btn btn-secondary">Test Auto</button>
            </div>

            <div class="test-card">
                <h3>🔧 API Backend</h3>
                <p>Test de l'API PHP pour génération de PDFs et traitement des données côté serveur.</p>
                <button onclick="testAPI()" class="btn btn-warning">Test API</button>
                <button onclick="checkPHPSupport()" class="btn btn-secondary">Check PHP</button>
            </div>
        </div>

        <div class="status-section">
            <h3>📊 Status des Tests</h3>
            <div id="testResults">
                <div class="status-item">
                    <span>Serveur Local</span>
                    <span id="serverStatus" class="status-ok">✅ Actif</span>
                </div>
                <div class="status-item">
                    <span>Fichiers HTML</span>
                    <span id="htmlStatus">⏳ Vérification...</span>
                </div>
                <div class="status-item">
                    <span>JavaScript</span>
                    <span id="jsStatus">⏳ Vérification...</span>
                </div>
                <div class="status-item">
                    <span>CSS Responsive</span>
                    <span id="cssStatus">⏳ Vérification...</span>
                </div>
            </div>
        </div>

        <div class="instructions">
            <h4>📋 Instructions de Testing</h4>
            <ol>
                <li><strong>Démarrer le serveur:</strong> <code>python test_server.py</code></li>
                <li><strong>Ouvrir le navigateur:</strong> <code>http://localhost:8000</code></li>
                <li><strong>Tester chaque formulaire:</strong> Remplir avec des données réelles</li>
                <li><strong>Vérifier l'impression:</strong> Ctrl+P pour voir le rendu</li>
                <li><strong>Test mobile:</strong> F12 → Mode responsive</li>
                <li><strong>Test de validation:</strong> Essayer de soumettre avec des champs vides</li>
            </ol>
        </div>

        <div style="text-align: center; margin-top: 30px;">
            <button onclick="runAllTests()" class="btn" style="font-size: 1.2rem; padding: 15px 30px;">
                🚀 Lancer Tous les Tests
            </button>
        </div>
    </div>

    <script>
        // Vérifications automatiques au chargement
        document.addEventListener('DOMContentLoaded', function() {
            checkFiles();
            checkJavaScript();
            checkCSS();
        });

        function checkFiles() {
            const files = ['generador_vtc_paris.html', 'generador_bon_reservation.html'];
            let allFound = true;
            
            files.forEach(file => {
                fetch(file)
                    .then(response => {
                        if (!response.ok) {
                            allFound = false;
                        }
                    })
                    .catch(() => {
                        allFound = false;
                    });
            });
            
            setTimeout(() => {
                document.getElementById('htmlStatus').innerHTML = allFound ? 
                    '<span class="status-ok">✅ Trouvés</span>' : 
                    '<span class="status-error">❌ Manquants</span>';
            }, 1000);
        }

        function checkJavaScript() {
            try {
                // Test basique de JavaScript
                const test = new Date().toISOString();
                document.getElementById('jsStatus').innerHTML = '<span class="status-ok">✅ Fonctionnel</span>';
            } catch (e) {
                document.getElementById('jsStatus').innerHTML = '<span class="status-error">❌ Erreur</span>';
            }
        }

        function checkCSS() {
            // Test de support CSS Grid et Flexbox
            const testDiv = document.createElement('div');
            testDiv.style.display = 'grid';
            const supportsGrid = testDiv.style.display === 'grid';
            
            document.getElementById('cssStatus').innerHTML = supportsGrid ? 
                '<span class="status-ok">✅ Compatible</span>' : 
                '<span class="status-error">❌ Limité</span>';
        }

        function testVTC() {
            // Ouvrir et pré-remplir le formulaire VTC
            const vtcWindow = window.open('generador_vtc_paris.html', '_blank');
            
            setTimeout(() => {
                if (vtcWindow) {
                    alert('✅ Formulaire VTC ouvert. Testez la génération de document.');
                }
            }, 1000);
        }

        function testReservation() {
            // Ouvrir et pré-remplir le formulaire de réservation
            const resWindow = window.open('generador_bon_reservation.html', '_blank');
            
            setTimeout(() => {
                if (resWindow) {
                    alert('✅ Formulaire Réservation ouvert. Testez avec plusieurs services.');
                }
            }, 1000);
        }

        function testAPI() {
            // Test de l'API PHP
            fetch('bon_reservation_api.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'action=test'
            })
            .then(response => response.json())
            .then(data => {
                alert('✅ API Response: ' + JSON.stringify(data));
            })
            .catch(error => {
                alert('❌ API Error: ' + error.message);
            });
        }

        function checkPHPSupport() {
            fetch('bon_reservation_api.php')
                .then(response => {
                    if (response.ok) {
                        alert('✅ PHP est supporté sur ce serveur');
                    } else {
                        alert('❌ PHP non disponible ou erreur de configuration');
                    }
                })
                .catch(error => {
                    alert('❌ Impossible de tester PHP: ' + error.message);
                });
        }

        function runAllTests() {
            alert('🚀 Lancement des tests automatiques...');
            
            // Séquence de tests
            setTimeout(() => testVTC(), 500);
            setTimeout(() => testReservation(), 1500);
            setTimeout(() => testAPI(), 2500);
            
            setTimeout(() => {
                alert('✅ Tests terminés! Vérifiez les résultats dans les nouvelles fenêtres.');
            }, 3500);
        }

        // Test de responsive design
        function testResponsive() {
            const width = window.innerWidth;
            if (width < 768) {
                alert('📱 Mode mobile détecté - Testez les formulaires sur mobile');
            } else {
                alert('🖥️ Mode desktop - Utilisez F12 pour tester le responsive');
            }
        }

        // Appeler le test responsive au redimensionnement
        window.addEventListener('resize', testResponsive);
    </script>
</body>
</html>
