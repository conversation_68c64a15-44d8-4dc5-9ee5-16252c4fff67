<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bon <PERSON> Réservation - Paris Elite Services</title>
    <style>
        @page {
            size: A4;
            margin: 1cm;
        }
        body {
            font-family: Arial, sans-serif;
            font-size: 12pt;
            line-height: 1.4;
            margin: 0;
            padding: 20px;
            color: #333;
        }
        .container {
            max-width: 21cm;
            margin: 0 auto;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 3px solid #0066cc;
            padding-bottom: 15px;
        }
        .header h1 {
            color: #0066cc;
            font-size: 24pt;
            margin: 0;
        }
        .reservation-info {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
            font-weight: bold;
        }
        .section {
            margin-bottom: 25px;
        }
        .section h2 {
            background-color: #0066cc;
            color: white;
            padding: 8px 15px;
            margin: 0 0 15px 0;
            font-size: 14pt;
        }
        .client-info, .service-info {
            background-color: #f8f9fa;
            padding: 15px;
            border-left: 4px solid #0066cc;
        }
        .service-item {
            border: 1px solid #ddd;
            margin-bottom: 15px;
            padding: 15px;
            background-color: white;
        }
        .service-date {
            background-color: #0066cc;
            color: white;
            padding: 5px 10px;
            margin: -15px -15px 10px -15px;
            font-weight: bold;
        }
        .service-details {
            margin: 10px 0;
        }
        .service-details strong {
            color: #0066cc;
        }
        .financial-summary {
            background-color: #e8f4f8;
            padding: 15px;
            border: 2px solid #0066cc;
        }
        .financial-summary table {
            width: 100%;
            border-collapse: collapse;
        }
        .financial-summary td {
            padding: 8px;
            border-bottom: 1px solid #ccc;
        }
        .financial-summary .total {
            font-weight: bold;
            font-size: 14pt;
            background-color: #0066cc;
            color: white;
        }
        .conditions {
            font-size: 10pt;
            background-color: #f8f9fa;
            padding: 15px;
            border: 1px solid #ddd;
        }
        .contact-urgence {
            background-color: #fff3cd;
            border: 2px solid #ffc107;
            padding: 15px;
            text-align: center;
        }
        .signature-section {
            margin-top: 30px;
            text-align: right;
        }
        .certification {
            font-style: italic;
            text-align: center;
            margin: 20px 0;
            padding: 10px;
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
        }
        @media print {
            body { margin: 0; }
            .no-print { display: none; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>BON DE RÉSERVATION</h1>
            <h2 style="color: #0066cc; margin: 5px 0;">PARIS ELITE SERVICES</h2>
        </div>

        <div class="reservation-info">
            <div>N° de Réservation: <span id="numeroReservation">[NUMERO_RESERVATION]</span></div>
            <div>Date d'émission: <span id="dateEmission">[DATE_EMISSION]</span></div>
        </div>

        <div class="section">
            <h2>INFORMATIONS CLIENT</h2>
            <div class="client-info">
                <p><strong>Nom:</strong> <span id="nomClient">[NOM_CLIENT]</span></p>
                <p><strong>Prénom:</strong> <span id="prenomClient">[PRENOM_CLIENT]</span></p>
                <p><strong>Nationalité:</strong> <span id="nationalite">[NATIONALITE]</span></p>
                <p><strong>Nombre de passagers:</strong> <span id="nombrePassagers">[NOMBRE_PASSAGERS]</span> personne(s)</p>
                <p><strong>Téléphone:</strong> <span id="telephone">[TELEPHONE]</span></p>
                <p><strong>Email:</strong> <span id="email">[EMAIL]</span></p>
            </div>
        </div>

        <div class="section">
            <h2>PRESTATIONS RÉSERVÉES</h2>
            <div id="servicesContainer">
                <!-- Services will be dynamically added here -->
            </div>
        </div>

        <div class="section">
            <h2>RÉCAPITULATIF FINANCIER</h2>
            <div class="financial-summary">
                <table>
                    <tr>
                        <td>Montant total des prestations:</td>
                        <td style="text-align: right;"><span id="montantTotal">[MONTANT_TOTAL]</span> €</td>
                    </tr>
                    <tr>
                        <td>Acompte versé:</td>
                        <td style="text-align: right;"><span id="acompte">[ACOMPTE]</span> €</td>
                    </tr>
                    <tr class="total">
                        <td>Solde à régler:</td>
                        <td style="text-align: right;"><span id="solde">[SOLDE]</span> €</td>
                    </tr>
                </table>
                <p style="margin-top: 10px;"><strong>Mode de paiement:</strong> <span id="modePaiement">[MODE_PAIEMENT]</span></p>
            </div>
        </div>

        <div class="section">
            <h2>CONDITIONS GÉNÉRALES</h2>
            <div class="conditions">
                <ul>
                    <li><strong>Confirmation de réservation:</strong> Cette réservation est confirmée et garantie</li>
                    <li><strong>Annulation:</strong> Possible jusqu'à 72h avant le service sans frais</li>
                    <li><strong>Modification:</strong> Possible sous réserve de disponibilité</li>
                    <li><strong>Assurance:</strong> Véhicules assurés et chauffeurs professionnels</li>
                    <li><strong>Langue:</strong> Service disponible en français, espagnol et anglais</li>
                </ul>
            </div>
        </div>

        <div class="section">
            <h2>CONTACT URGENCE</h2>
            <div class="contact-urgence">
                <p><strong>PARIS ELITE SERVICES</strong><br>
                Boris Porras del Castillo</p>
                <p><strong>Téléphone 24h/24:</strong> +33 6 68 25 11 02<br>
                <strong>Email:</strong> <EMAIL></p>
                <p><strong>Adresse:</strong> 11 allée Dumont d'Urville, 77200 TORCY<br>
                <strong>SIRET:</strong> 507 650 331 00022</p>
            </div>
        </div>

        <div class="certification">
            <em>Ce document certifie la réservation effective des prestations mentionnées ci-dessus et peut être présenté aux autorités en cas de contrôle.</em>
        </div>

        <div class="signature-section">
            <p><strong>Date et signature:</strong><br>
            Paris, le <span id="dateSignature">[DATE_SIGNATURE]</span></p>
            <br>
            <p><strong>PARIS ELITE SERVICES</strong><br>
            Boris Porras del Castillo<br>
            <em>Gérant</em></p>
        </div>
    </div>

    <script>
        // Template for service item
        function createServiceItem(service) {
            return `
                <div class="service-item">
                    <div class="service-date">${service.date} - ${service.jour}</div>
                    <div class="service-details">
                        <p><strong>${service.heure}</strong> - <strong>${service.type}</strong></p>
                        <p>${service.description}</p>
                        <p><strong>Lieu de prise en charge:</strong> ${service.lieuPriseEnCharge}</p>
                        <p><strong>Destination:</strong> ${service.destination}</p>
                        <p><strong>Durée:</strong> ${service.duree}</p>
                        <p><strong>Tarif:</strong> ${service.tarif} €</p>
                    </div>
                </div>
            `;
        }

        // Function to populate the document with data
        function populateDocument(data) {
            // Basic info
            document.getElementById('numeroReservation').textContent = data.numeroReservation;
            document.getElementById('dateEmission').textContent = data.dateEmission;
            
            // Client info
            document.getElementById('nomClient').textContent = data.nomClient;
            document.getElementById('prenomClient').textContent = data.prenomClient;
            document.getElementById('nationalite').textContent = data.nationalite;
            document.getElementById('nombrePassagers').textContent = data.nombrePassagers;
            document.getElementById('telephone').textContent = data.telephone;
            document.getElementById('email').textContent = data.email;
            
            // Services
            const servicesContainer = document.getElementById('servicesContainer');
            servicesContainer.innerHTML = '';
            data.services.forEach(service => {
                servicesContainer.innerHTML += createServiceItem(service);
            });
            
            // Financial info
            document.getElementById('montantTotal').textContent = data.montantTotal;
            document.getElementById('acompte').textContent = data.acompte;
            document.getElementById('solde').textContent = data.solde;
            document.getElementById('modePaiement').textContent = data.modePaiement;
            document.getElementById('dateSignature').textContent = data.dateSignature;
        }
    </script>
</body>
</html>
