#!/usr/bin/env python3
"""
Servidor de testing local para los generadores de documentos
Uso: python test_server.py
Luego abrir: http://localhost:8000
"""

import http.server
import socketserver
import webbrowser
import os
import sys
from pathlib import Path

# Configuración
PORT = 8000
DIRECTORY = "."

class CustomHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, directory=DIRECTORY, **kwargs)
    
    def end_headers(self):
        # Añadir headers CORS para testing
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        super().end_headers()
    
    def do_OPTIONS(self):
        # Manejar preflight requests
        self.send_response(200)
        self.end_headers()
    
    def log_message(self, format, *args):
        # Log personalizado
        print(f"[{self.address_string()}] {format % args}")

def main():
    print("🚀 Iniciando servidor de testing...")
    print(f"📁 Directorio: {os.path.abspath(DIRECTORY)}")
    print(f"🌐 URL: http://localhost:{PORT}")
    print("📋 Archivos disponibles:")
    
    # Listar archivos HTML disponibles
    html_files = list(Path(DIRECTORY).glob("*.html"))
    for i, file in enumerate(html_files, 1):
        print(f"   {i}. http://localhost:{PORT}/{file.name}")
    
    print("\n⚠️  Para detener el servidor: Ctrl+C")
    print("=" * 50)
    
    try:
        with socketserver.TCPServer(("", PORT), CustomHTTPRequestHandler) as httpd:
            print(f"✅ Servidor iniciado en puerto {PORT}")
            
            # Abrir navegador automáticamente
            if len(html_files) > 0:
                webbrowser.open(f'http://localhost:{PORT}/{html_files[0].name}')
            else:
                webbrowser.open(f'http://localhost:{PORT}')
            
            httpd.serve_forever()
            
    except KeyboardInterrupt:
        print("\n🛑 Servidor detenido por el usuario")
    except OSError as e:
        if e.errno == 10048:  # Puerto en uso
            print(f"❌ Error: Puerto {PORT} ya está en uso")
            print(f"💡 Intenta con otro puerto o cierra la aplicación que lo usa")
        else:
            print(f"❌ Error: {e}")
    except Exception as e:
        print(f"❌ Error inesperado: {e}")

if __name__ == "__main__":
    main()
