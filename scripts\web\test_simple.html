<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Simple - Bon <PERSON></title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            color: #0066cc;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        
        input, select, textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }
        
        .btn {
            background: #0066cc;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        
        .btn:hover {
            background: #0052a3;
        }
        
        .btn-success {
            background: #28a745;
        }
        
        .btn-secondary {
            background: #6c757d;
        }
        
        .preview {
            margin-top: 30px;
            padding: 20px;
            border: 2px solid #0066cc;
            border-radius: 5px;
            background: #f8f9fa;
            display: none;
        }
        
        .preview.show {
            display: block;
        }
        
        .document {
            background: white;
            padding: 20px;
            border-radius: 5px;
            margin-top: 15px;
        }
        
        .document h1 {
            color: #0066cc;
            text-align: center;
            margin-bottom: 20px;
        }
        
        .info-section {
            margin: 15px 0;
            padding: 15px;
            background: #f8f9fa;
            border-left: 4px solid #0066cc;
        }
        
        .service-item {
            border: 1px solid #ddd;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Test Simple - Bon de Réservation</h1>
            <p>PARIS ELITE SERVICES</p>
        </div>

        <div class="form-group">
            <label for="nomClient">Nom du client:</label>
            <input type="text" id="nomClient" placeholder="GARCIA">
        </div>

        <div class="form-group">
            <label for="prenomClient">Prénom:</label>
            <input type="text" id="prenomClient" placeholder="Maria">
        </div>

        <div class="form-group">
            <label for="telephone">Téléphone:</label>
            <input type="text" id="telephone" placeholder="+34 612 345 678">
        </div>

        <div class="form-group">
            <label for="serviceType">Type de service:</label>
            <select id="serviceType">
                <option value="">Sélectionner...</option>
                <option value="Transfert aéroport">Transfert aéroport</option>
                <option value="Tour de ville">Tour de ville</option>
                <option value="Visite guidée">Visite guidée</option>
            </select>
        </div>

        <div class="form-group">
            <label for="montant">Montant (€):</label>
            <input type="number" id="montant" placeholder="140">
        </div>

        <div style="text-align: center; margin: 20px 0;">
            <button class="btn" onclick="fillExample()">Remplir Exemple</button>
            <button class="btn btn-success" onclick="generateDocument()">Générer Document</button>
            <button class="btn btn-secondary" onclick="clearForm()">Effacer</button>
        </div>

        <div id="preview" class="preview">
            <h3>Aperçu du Document</h3>
            <div id="document" class="document">
                <!-- Le document sera généré ici -->
            </div>
            <div style="text-align: center; margin-top: 15px;">
                <button class="btn" onclick="printDoc()">Imprimer</button>
            </div>
        </div>
    </div>

    <script>
        console.log('Script chargé');

        function fillExample() {
            console.log('fillExample appelée');
            document.getElementById('nomClient').value = 'GARCIA';
            document.getElementById('prenomClient').value = 'Maria';
            document.getElementById('telephone').value = '+34 612 345 678';
            document.getElementById('serviceType').value = 'Transfert aéroport';
            document.getElementById('montant').value = '140';
            alert('Données d\'exemple remplies!');
        }

        function generateDocument() {
            console.log('generateDocument appelée');
            
            const nom = document.getElementById('nomClient').value;
            const prenom = document.getElementById('prenomClient').value;
            const telephone = document.getElementById('telephone').value;
            const service = document.getElementById('serviceType').value;
            const montant = document.getElementById('montant').value;

            if (!nom || !prenom || !service) {
                alert('Veuillez remplir au moins le nom, prénom et type de service');
                return;
            }

            const today = new Date().toLocaleDateString('fr-FR');
            const reservationNumber = 'PES' + Date.now();

            const documentHTML = `
                <h1>BON DE RÉSERVATION</h1>
                <h2 style="text-align: center; color: #0066cc;">PARIS ELITE SERVICES</h2>
                
                <p><strong>N° de Réservation:</strong> ${reservationNumber}</p>
                <p><strong>Date d'émission:</strong> ${today}</p>
                
                <div class="info-section">
                    <h3>INFORMATIONS CLIENT</h3>
                    <p><strong>Nom:</strong> ${nom} ${prenom}</p>
                    <p><strong>Téléphone:</strong> ${telephone}</p>
                </div>
                
                <div class="info-section">
                    <h3>PRESTATIONS RÉSERVÉES</h3>
                    <div class="service-item">
                        <p><strong>Service:</strong> ${service}</p>
                        <p><strong>Tarif:</strong> ${montant} €</p>
                    </div>
                </div>
                
                <div class="info-section">
                    <h3>CONTACT URGENCE</h3>
                    <p><strong>PARIS ELITE SERVICES</strong><br>
                    Boris Porras del Castillo<br>
                    <strong>Téléphone:</strong> +33 6 68 25 11 02<br>
                    <strong>Email:</strong> <EMAIL><br>
                    <strong>SIRET:</strong> 507 650 331 00022</p>
                </div>
                
                <div style="text-align: center; margin-top: 30px; padding: 15px; background: #d4edda; border-radius: 5px;">
                    <em><strong>Ce document certifie la réservation effective des prestations mentionnées ci-dessus et peut être présenté aux autorités en cas de contrôle.</strong></em>
                </div>
                
                <div style="text-align: center; margin-top: 30px;">
                    <p><strong>Date et signature:</strong><br>
                    Paris, le ${today}</p>
                    <br>
                    <p><strong>PARIS ELITE SERVICES</strong><br>
                    Boris Porras del Castillo<br>
                    <em>Gérant</em></p>
                </div>
            `;

            document.getElementById('document').innerHTML = documentHTML;
            document.getElementById('preview').classList.add('show');
            
            // Scroll vers l'aperçu
            document.getElementById('preview').scrollIntoView({ behavior: 'smooth' });
        }

        function clearForm() {
            console.log('clearForm appelée');
            document.getElementById('nomClient').value = '';
            document.getElementById('prenomClient').value = '';
            document.getElementById('telephone').value = '';
            document.getElementById('serviceType').value = '';
            document.getElementById('montant').value = '';
            document.getElementById('preview').classList.remove('show');
        }

        function printDoc() {
            console.log('printDoc appelée');
            const printContent = document.getElementById('document').innerHTML;
            const printWindow = window.open('', '_blank');
            printWindow.document.write(`
                <!DOCTYPE html>
                <html>
                <head>
                    <title>Bon de Réservation</title>
                    <style>
                        body { font-family: Arial, sans-serif; padding: 20px; }
                        .info-section { margin: 15px 0; padding: 15px; background: #f8f9fa; border-left: 4px solid #0066cc; }
                        .service-item { border: 1px solid #ddd; padding: 10px; margin: 10px 0; }
                    </style>
                </head>
                <body>${printContent}</body>
                </html>
            `);
            printWindow.document.close();
            printWindow.print();
        }

        // Test que le script fonctionne
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM chargé, script prêt');
        });
    </script>
</body>
</html>
