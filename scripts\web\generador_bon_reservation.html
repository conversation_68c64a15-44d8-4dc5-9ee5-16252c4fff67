<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Générateur de Bon de Réservation - Paris Elite Services</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            color: #0066cc;
        }
        .form-section {
            margin-bottom: 25px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #f8f9fa;
        }
        .form-section h3 {
            color: #0066cc;
            margin-top: 0;
            border-bottom: 2px solid #0066cc;
            padding-bottom: 10px;
        }
        .form-row {
            display: flex;
            gap: 15px;
            margin-bottom: 15px;
        }
        .form-group {
            flex: 1;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        input, select, textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        textarea {
            height: 60px;
            resize: vertical;
        }
        .service-item {
            border: 1px solid #ccc;
            padding: 15px;
            margin-bottom: 15px;
            border-radius: 5px;
            background-color: white;
        }
        .service-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
        }
        .btn-primary {
            background-color: #0066cc;
            color: white;
        }
        .btn-success {
            background-color: #28a745;
            color: white;
        }
        .btn-danger {
            background-color: #dc3545;
            color: white;
        }
        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }
        .btn:hover {
            opacity: 0.8;
        }
        .actions {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 2px solid #0066cc;
        }
        .preview-section {
            margin-top: 30px;
            padding: 20px;
            border: 2px solid #0066cc;
            border-radius: 5px;
            background-color: #f0f8ff;
        }
        #preview {
            width: 100%;
            height: 600px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Générateur de Bon de Réservation</h1>
            <h2>PARIS ELITE SERVICES</h2>
        </div>

        <form id="reservationForm">
            <!-- Informations de base -->
            <div class="form-section">
                <h3>Informations de Réservation</h3>
                <div class="form-row">
                    <div class="form-group">
                        <label for="numeroReservation">N° de Réservation:</label>
                        <input type="text" id="numeroReservation" name="numeroReservation" required>
                    </div>
                    <div class="form-group">
                        <label for="dateEmission">Date d'émission:</label>
                        <input type="date" id="dateEmission" name="dateEmission" required>
                    </div>
                </div>
            </div>

            <!-- Informations client -->
            <div class="form-section">
                <h3>Informations Client</h3>
                <div class="form-row">
                    <div class="form-group">
                        <label for="nomClient">Nom:</label>
                        <input type="text" id="nomClient" name="nomClient" required>
                    </div>
                    <div class="form-group">
                        <label for="prenomClient">Prénom:</label>
                        <input type="text" id="prenomClient" name="prenomClient" required>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="nationalite">Nationalité:</label>
                        <input type="text" id="nationalite" name="nationalite" required>
                    </div>
                    <div class="form-group">
                        <label for="nombrePassagers">Nombre de passagers:</label>
                        <input type="number" id="nombrePassagers" name="nombrePassagers" min="1" required>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="telephone">Téléphone:</label>
                        <input type="tel" id="telephone" name="telephone" required>
                    </div>
                    <div class="form-group">
                        <label for="email">Email:</label>
                        <input type="email" id="email" name="email" required>
                    </div>
                </div>
            </div>

            <!-- Services -->
            <div class="form-section">
                <h3>Prestations Réservées</h3>
                <div id="servicesContainer">
                    <!-- Services will be added dynamically -->
                </div>
                <button type="button" class="btn btn-success" onclick="addService()">+ Ajouter un Service</button>
            </div>

            <!-- Informations financières -->
            <div class="form-section">
                <h3>Récapitulatif Financier</h3>
                <div class="form-row">
                    <div class="form-group">
                        <label for="montantTotal">Montant total (€):</label>
                        <input type="number" id="montantTotal" name="montantTotal" step="0.01" required>
                    </div>
                    <div class="form-group">
                        <label for="acompte">Acompte versé (€):</label>
                        <input type="number" id="acompte" name="acompte" step="0.01" value="0">
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="solde">Solde à régler (€):</label>
                        <input type="number" id="solde" name="solde" step="0.01" readonly>
                    </div>
                    <div class="form-group">
                        <label for="modePaiement">Mode de paiement:</label>
                        <select id="modePaiement" name="modePaiement" required>
                            <option value="">Sélectionner...</option>
                            <option value="Espèces">Espèces</option>
                            <option value="Carte bancaire">Carte bancaire</option>
                            <option value="Virement bancaire">Virement bancaire</option>
                            <option value="PayPal">PayPal</option>
                        </select>
                    </div>
                </div>
            </div>

            <div class="actions">
                <button type="button" class="btn btn-primary" onclick="generatePreview()">Générer Aperçu</button>
                <button type="button" class="btn btn-success" onclick="generatePDF()">Télécharger PDF</button>
                <button type="button" class="btn btn-secondary" onclick="resetForm()">Réinitialiser</button>
            </div>
        </form>

        <div class="preview-section" id="previewSection" style="display: none;">
            <h3>Aperçu du Document</h3>
            <iframe id="preview" src=""></iframe>
            <div style="text-align: center; margin-top: 15px;">
                <button type="button" class="btn btn-primary" onclick="printDocument()">Imprimer</button>
            </div>
        </div>
    </div>

    <script>
        let serviceCounter = 0;

        // Initialize form
        document.addEventListener('DOMContentLoaded', function() {
            // Set today's date as default
            document.getElementById('dateEmission').value = new Date().toISOString().split('T')[0];
            
            // Add first service
            addService();
            
            // Calculate solde automatically
            document.getElementById('montantTotal').addEventListener('input', calculateSolde);
            document.getElementById('acompte').addEventListener('input', calculateSolde);
        });

        function addService() {
            serviceCounter++;
            const container = document.getElementById('servicesContainer');
            const serviceDiv = document.createElement('div');
            serviceDiv.className = 'service-item';
            serviceDiv.id = `service-${serviceCounter}`;
            
            serviceDiv.innerHTML = `
                <div class="service-header">
                    <h4>Service ${serviceCounter}</h4>
                    <button type="button" class="btn btn-danger" onclick="removeService(${serviceCounter})">Supprimer</button>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label>Date du service:</label>
                        <input type="date" name="service_date_${serviceCounter}" required>
                    </div>
                    <div class="form-group">
                        <label>Heure:</label>
                        <input type="time" name="service_heure_${serviceCounter}" required>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label>Type de service:</label>
                        <select name="service_type_${serviceCounter}" required>
                            <option value="">Sélectionner...</option>
                            <option value="Transfert aéroport">Transfert aéroport</option>
                            <option value="Transfert gare">Transfert gare</option>
                            <option value="Tour de ville">Tour de ville</option>
                            <option value="Visite guidée">Visite guidée</option>
                            <option value="Mise à disposition">Mise à disposition</option>
                            <option value="Autre">Autre</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Tarif (€):</label>
                        <input type="number" name="service_tarif_${serviceCounter}" step="0.01" required>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label>Description:</label>
                        <textarea name="service_description_${serviceCounter}" placeholder="Description détaillée du service"></textarea>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label>Lieu de prise en charge:</label>
                        <input type="text" name="service_lieu_${serviceCounter}" required>
                    </div>
                    <div class="form-group">
                        <label>Destination:</label>
                        <input type="text" name="service_destination_${serviceCounter}" required>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label>Durée:</label>
                        <input type="text" name="service_duree_${serviceCounter}" placeholder="ex: 2 heures" required>
                    </div>
                </div>
            `;
            
            container.appendChild(serviceDiv);
        }

        function removeService(id) {
            const serviceDiv = document.getElementById(`service-${id}`);
            if (serviceDiv) {
                serviceDiv.remove();
            }
        }

        function calculateSolde() {
            const montantTotal = parseFloat(document.getElementById('montantTotal').value) || 0;
            const acompte = parseFloat(document.getElementById('acompte').value) || 0;
            const solde = montantTotal - acompte;
            document.getElementById('solde').value = solde.toFixed(2);
        }

        function generatePreview() {
            const formData = collectFormData();
            
            // Create preview URL with data
            const previewUrl = createPreviewDocument(formData);
            
            // Show preview
            document.getElementById('preview').src = previewUrl;
            document.getElementById('previewSection').style.display = 'block';
            
            // Scroll to preview
            document.getElementById('previewSection').scrollIntoView({ behavior: 'smooth' });
        }

        function collectFormData() {
            const form = document.getElementById('reservationForm');
            const formData = new FormData(form);
            
            const data = {
                numeroReservation: formData.get('numeroReservation'),
                dateEmission: formatDate(formData.get('dateEmission')),
                nomClient: formData.get('nomClient'),
                prenomClient: formData.get('prenomClient'),
                nationalite: formData.get('nationalite'),
                nombrePassagers: formData.get('nombrePassagers'),
                telephone: formData.get('telephone'),
                email: formData.get('email'),
                montantTotal: formData.get('montantTotal'),
                acompte: formData.get('acompte'),
                solde: formData.get('solde'),
                modePaiement: formData.get('modePaiement'),
                dateSignature: formatDate(new Date().toISOString().split('T')[0]),
                services: []
            };
            
            // Collect services
            for (let i = 1; i <= serviceCounter; i++) {
                const serviceDiv = document.getElementById(`service-${i}`);
                if (serviceDiv) {
                    const service = {
                        date: formatDate(formData.get(`service_date_${i}`)),
                        jour: getDayName(formData.get(`service_date_${i}`)),
                        heure: formData.get(`service_heure_${i}`),
                        type: formData.get(`service_type_${i}`),
                        description: formData.get(`service_description_${i}`),
                        lieuPriseEnCharge: formData.get(`service_lieu_${i}`),
                        destination: formData.get(`service_destination_${i}`),
                        duree: formData.get(`service_duree_${i}`),
                        tarif: formData.get(`service_tarif_${i}`)
                    };
                    data.services.push(service);
                }
            }
            
            return data;
        }

        function createPreviewDocument(data) {
            // Create a blob with the HTML template filled with data
            const templateUrl = '../../plantillas/bon_reservation.html';
            
            // For now, we'll create a data URL with the filled template
            // In a real implementation, this would be handled server-side
            const htmlContent = generateHTMLContent(data);
            const blob = new Blob([htmlContent], { type: 'text/html' });
            return URL.createObjectURL(blob);
        }

        function generateHTMLContent(data) {
            // This is a simplified version - in production, you'd load the template and replace placeholders
            return `
                <!DOCTYPE html>
                <html>
                <head>
                    <title>Bon de Réservation</title>
                    <style>
                        body { font-family: Arial, sans-serif; padding: 20px; }
                        .header { text-align: center; color: #0066cc; }
                        .section { margin: 20px 0; }
                        .service-item { border: 1px solid #ddd; padding: 15px; margin: 10px 0; }
                    </style>
                </head>
                <body>
                    <div class="header">
                        <h1>BON DE RÉSERVATION</h1>
                        <h2>PARIS ELITE SERVICES</h2>
                    </div>
                    <p><strong>N° de Réservation:</strong> ${data.numeroReservation}</p>
                    <p><strong>Date d'émission:</strong> ${data.dateEmission}</p>
                    
                    <div class="section">
                        <h3>INFORMATIONS CLIENT</h3>
                        <p><strong>Nom:</strong> ${data.nomClient} ${data.prenomClient}</p>
                        <p><strong>Nationalité:</strong> ${data.nationalite}</p>
                        <p><strong>Passagers:</strong> ${data.nombrePassagers}</p>
                        <p><strong>Contact:</strong> ${data.telephone} - ${data.email}</p>
                    </div>
                    
                    <div class="section">
                        <h3>PRESTATIONS RÉSERVÉES</h3>
                        ${data.services.map(service => `
                            <div class="service-item">
                                <h4>${service.date} - ${service.heure}</h4>
                                <p><strong>${service.type}</strong></p>
                                <p>${service.description}</p>
                                <p><strong>De:</strong> ${service.lieuPriseEnCharge} <strong>À:</strong> ${service.destination}</p>
                                <p><strong>Durée:</strong> ${service.duree} - <strong>Tarif:</strong> ${service.tarif} €</p>
                            </div>
                        `).join('')}
                    </div>
                    
                    <div class="section">
                        <h3>RÉCAPITULATIF FINANCIER</h3>
                        <p><strong>Total:</strong> ${data.montantTotal} €</p>
                        <p><strong>Acompte:</strong> ${data.acompte} €</p>
                        <p><strong>Solde:</strong> ${data.solde} €</p>
                        <p><strong>Paiement:</strong> ${data.modePaiement}</p>
                    </div>
                    
                    <div style="text-align: center; margin-top: 30px;">
                        <p><em>Ce document certifie la réservation effective des prestations mentionnées.</em></p>
                        <p><strong>PARIS ELITE SERVICES</strong><br>Boris Porras del Castillo</p>
                    </div>
                </body>
                </html>
            `;
        }

        function formatDate(dateString) {
            if (!dateString) return '';
            const date = new Date(dateString);
            return date.toLocaleDateString('fr-FR');
        }

        function getDayName(dateString) {
            if (!dateString) return '';
            const date = new Date(dateString);
            const days = ['Dimanche', 'Lundi', 'Mardi', 'Mercredi', 'Jeudi', 'Vendredi', 'Samedi'];
            return days[date.getDay()];
        }

        function generatePDF() {
            alert('Fonctionnalité PDF en développement. Utilisez l\'aperçu et imprimez pour le moment.');
        }

        function printDocument() {
            const iframe = document.getElementById('preview');
            iframe.contentWindow.print();
        }

        function resetForm() {
            if (confirm('Êtes-vous sûr de vouloir réinitialiser le formulaire ?')) {
                document.getElementById('reservationForm').reset();
                document.getElementById('servicesContainer').innerHTML = '';
                serviceCounter = 0;
                addService();
                document.getElementById('previewSection').style.display = 'none';
            }
        }
    </script>
</body>
</html>
