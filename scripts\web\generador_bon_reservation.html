<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Générateur de Bon de Réservation - Paris Elite Services</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            color: #0066cc;
        }
        .form-section {
            margin-bottom: 25px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #f8f9fa;
        }
        .form-section h3 {
            color: #0066cc;
            margin-top: 0;
            border-bottom: 2px solid #0066cc;
            padding-bottom: 10px;
        }
        .form-row {
            display: flex;
            gap: 15px;
            margin-bottom: 15px;
        }
        .form-group {
            flex: 1;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        input, select, textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        textarea {
            height: 60px;
            resize: vertical;
        }
        .service-item {
            border: 1px solid #ccc;
            padding: 15px;
            margin-bottom: 15px;
            border-radius: 5px;
            background-color: white;
        }
        .service-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
        }
        .btn-primary {
            background-color: #0066cc;
            color: white;
        }
        .btn-success {
            background-color: #28a745;
            color: white;
        }
        .btn-danger {
            background-color: #dc3545;
            color: white;
        }
        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }
        .btn:hover {
            opacity: 0.8;
        }
        .actions {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 2px solid #0066cc;
        }
        .preview-section {
            margin-top: 30px;
            padding: 20px;
            border: 2px solid #0066cc;
            border-radius: 5px;
            background-color: #f0f8ff;
        }
        #preview {
            width: 100%;
            height: 800px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Générateur de Bon de Réservation</h1>
            <h2>PARIS ELITE SERVICES</h2>
        </div>

        <form id="reservationForm">
            <!-- Informations de base -->
            <div class="form-section">
                <h3>Informations de Réservation</h3>
                <div class="form-row">
                    <div class="form-group">
                        <label for="numeroReservation">N° de Réservation:</label>
                        <input type="text" id="numeroReservation" name="numeroReservation" required>
                    </div>
                    <div class="form-group">
                        <label for="dateEmission">Date d'émission:</label>
                        <input type="date" id="dateEmission" name="dateEmission" required>
                    </div>
                </div>
            </div>

            <!-- Informations client -->
            <div class="form-section">
                <h3>Informations Client</h3>
                <div class="form-row">
                    <div class="form-group">
                        <label for="nomClient">Nom:</label>
                        <input type="text" id="nomClient" name="nomClient" required placeholder="GARCIA">
                    </div>
                    <div class="form-group">
                        <label for="prenomClient">Prénom:</label>
                        <input type="text" id="prenomClient" name="prenomClient" required placeholder="Maria">
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="nationalite">Nationalité:</label>
                        <input type="text" id="nationalite" name="nationalite" required placeholder="Espagnole">
                    </div>
                    <div class="form-group">
                        <label for="nombrePassagers">Nombre de passagers:</label>
                        <input type="number" id="nombrePassagers" name="nombrePassagers" min="1" required placeholder="2">
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="telephone">Téléphone:</label>
                        <input type="tel" id="telephone" name="telephone" required placeholder="+34 612 345 678">
                    </div>
                    <div class="form-group">
                        <label for="email">Email:</label>
                        <input type="email" id="email" name="email" required placeholder="<EMAIL>">
                    </div>
                </div>
            </div>

            <!-- Services -->
            <div class="form-section">
                <h3>Prestations Réservées</h3>
                <div id="servicesContainer">
                    <!-- Services will be added dynamically -->
                </div>
                <button type="button" class="btn btn-success" onclick="addService()">+ Ajouter un Service</button>
            </div>

            <!-- Informations financières -->
            <div class="form-section">
                <h3>Récapitulatif Financier</h3>
                <div class="form-row">
                    <div class="form-group">
                        <label for="montantTotal">Montant total (€):</label>
                        <input type="number" id="montantTotal" name="montantTotal" step="0.01" required>
                    </div>
                    <div class="form-group">
                        <label for="acompte">Acompte versé (€):</label>
                        <input type="number" id="acompte" name="acompte" step="0.01" value="0">
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label for="solde">Solde à régler (€):</label>
                        <input type="number" id="solde" name="solde" step="0.01" readonly>
                    </div>
                    <div class="form-group">
                        <label for="modePaiement">Mode de paiement:</label>
                        <select id="modePaiement" name="modePaiement" required>
                            <option value="">Sélectionner...</option>
                            <option value="Espèces">Espèces</option>
                            <option value="Carte bancaire">Carte bancaire</option>
                            <option value="Virement bancaire">Virement bancaire</option>
                            <option value="PayPal">PayPal</option>
                        </select>
                    </div>
                </div>
            </div>

            <div class="actions">
                <button type="button" class="btn btn-primary" onclick="generatePreview()">Générer Aperçu</button>
                <button type="button" class="btn btn-success" onclick="generatePDF()">Télécharger PDF</button>
                <button type="button" class="btn btn-secondary" onclick="fillExampleData()">Données d'Exemple</button>
                <button type="button" class="btn btn-secondary" onclick="resetForm()">Réinitialiser</button>
            </div>
        </form>

        <div class="preview-section" id="previewSection" style="display: none;">
            <h3>Aperçu du Document</h3>
            <iframe id="preview" src=""></iframe>
            <div style="text-align: center; margin-top: 15px;">
                <button type="button" class="btn btn-primary" onclick="printDocument()">Imprimer</button>
            </div>
        </div>
    </div>

    <script>
        let serviceCounter = 0;

        // Génération automatique du numéro de réservation
        function generateReservationNumber() {
            const date = new Date();
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            const random = Math.floor(Math.random() * 9999).toString().padStart(4, '0');
            return `PES${year}${month}${day}${random}`;
        }

        // Initialize form
        document.addEventListener('DOMContentLoaded', function() {
            // Set today's date as default
            document.getElementById('dateEmission').value = new Date().toISOString().split('T')[0];

            // Generate automatic reservation number
            document.getElementById('numeroReservation').value = generateReservationNumber();

            // Add first service
            addService();

            // Calculate solde automatically
            document.getElementById('montantTotal').addEventListener('input', calculateSolde);
            document.getElementById('acompte').addEventListener('input', calculateSolde);

            // Manejar el submit del formulario
            document.getElementById('reservationForm').addEventListener('submit', function(e) {
                e.preventDefault();
                generatePreview();
            });
        });

        function addService() {
            serviceCounter++;
            const container = document.getElementById('servicesContainer');
            const serviceDiv = document.createElement('div');
            serviceDiv.className = 'service-item';
            serviceDiv.id = `service-${serviceCounter}`;

            serviceDiv.innerHTML = `
                <div class="service-header">
                    <h4>Service ${serviceCounter}</h4>
                    <button type="button" class="btn btn-danger" onclick="removeService(${serviceCounter})">Supprimer</button>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label>Date du service:</label>
                        <input type="date" name="service_date_${serviceCounter}" required>
                    </div>
                    <div class="form-group">
                        <label>Heure:</label>
                        <input type="time" name="service_heure_${serviceCounter}" required>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label>Type de service:</label>
                        <select name="service_type_${serviceCounter}" required>
                            <option value="">Sélectionner...</option>
                            <option value="Transfert aéroport">Transfert aéroport</option>
                            <option value="Transfert gare">Transfert gare</option>
                            <option value="Tour de ville">Tour de ville</option>
                            <option value="Visite guidée">Visite guidée</option>
                            <option value="Mise à disposition">Mise à disposition</option>
                            <option value="Autre">Autre</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Tarif (€):</label>
                        <input type="number" name="service_tarif_${serviceCounter}" step="0.01" required>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label>Description:</label>
                        <textarea name="service_description_${serviceCounter}" placeholder="Description détaillée du service"></textarea>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label>Lieu de prise en charge:</label>
                        <input type="text" name="service_lieu_${serviceCounter}" required>
                    </div>
                    <div class="form-group">
                        <label>Destination:</label>
                        <input type="text" name="service_destination_${serviceCounter}" required>
                    </div>
                </div>
                <div class="form-row">
                    <div class="form-group">
                        <label>Durée:</label>
                        <input type="text" name="service_duree_${serviceCounter}" placeholder="ex: 2 heures" required>
                    </div>
                </div>
            `;

            container.appendChild(serviceDiv);
        }

        function removeService(id) {
            const serviceDiv = document.getElementById(`service-${id}`);
            if (serviceDiv) {
                serviceDiv.remove();
            }
        }

        function calculateSolde() {
            const montantTotal = parseFloat(document.getElementById('montantTotal').value) || 0;
            const acompte = parseFloat(document.getElementById('acompte').value) || 0;
            const solde = montantTotal - acompte;
            document.getElementById('solde').value = solde.toFixed(2);
        }

        function generatePreview() {
            const formData = collectFormData();

            // Validar campos requeridos
            if (!formData.numeroReservation || !formData.nomClient || !formData.prenomClient) {
                alert('Veuillez remplir tous les champs obligatoires');
                return;
            }

            if (formData.services.length === 0) {
                alert('Veuillez ajouter au moins un service');
                return;
            }

            // Create preview URL with data
            const previewUrl = createPreviewDocument(formData);

            // Show preview
            document.getElementById('preview').src = previewUrl;
            document.getElementById('previewSection').style.display = 'block';

            // Scroll to preview
            document.getElementById('previewSection').scrollIntoView({ behavior: 'smooth' });
        }

        function collectFormData() {
            const form = document.getElementById('reservationForm');
            const formData = new FormData(form);

            const data = {
                numeroReservation: formData.get('numeroReservation'),
                dateEmission: formatDate(formData.get('dateEmission')),
                nomClient: formData.get('nomClient'),
                prenomClient: formData.get('prenomClient'),
                nationalite: formData.get('nationalite'),
                nombrePassagers: formData.get('nombrePassagers'),
                telephone: formData.get('telephone'),
                email: formData.get('email'),
                montantTotal: formData.get('montantTotal'),
                acompte: formData.get('acompte'),
                solde: formData.get('solde'),
                modePaiement: formData.get('modePaiement'),
                dateSignature: formatDate(new Date().toISOString().split('T')[0]),
                services: []
            };

            // Collect services
            for (let i = 1; i <= serviceCounter; i++) {
                const serviceDiv = document.getElementById(`service-${i}`);
                if (serviceDiv) {
                    const service = {
                        date: formatDate(formData.get(`service_date_${i}`)),
                        jour: getDayName(formData.get(`service_date_${i}`)),
                        heure: formData.get(`service_heure_${i}`),
                        type: formData.get(`service_type_${i}`),
                        description: formData.get(`service_description_${i}`),
                        lieuPriseEnCharge: formData.get(`service_lieu_${i}`),
                        destination: formData.get(`service_destination_${i}`),
                        duree: formData.get(`service_duree_${i}`),
                        tarif: formData.get(`service_tarif_${i}`)
                    };
                    data.services.push(service);
                }
            }

            return data;
        }

        function createPreviewDocument(data) {
            // Create a blob with the HTML template filled with data
            const templateUrl = '../../plantillas/bon_reservation.html';

            // For now, we'll create a data URL with the filled template
            // In a real implementation, this would be handled server-side
            const htmlContent = generateHTMLContent(data);
            const blob = new Blob([htmlContent], { type: 'text/html' });
            return URL.createObjectURL(blob);
        }

        function generateHTMLContent(data) {
            return `
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bon de Réservation - Paris Elite Services</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            line-height: 1.6;
            color: #333;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 3px solid #0066cc;
            padding-bottom: 20px;
        }
        .header h1 {
            color: #0066cc;
            font-size: 2rem;
            margin: 0;
        }
        .header h2 {
            color: #0066cc;
            font-size: 1.5rem;
            margin: 10px 0 0 0;
        }
        .info-section {
            margin: 25px 0;
            padding: 20px;
            background: #f8f9fa;
            border-left: 5px solid #0066cc;
            border-radius: 5px;
        }
        .info-section h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.2rem;
        }
        .info-row {
            margin: 10px 0;
        }
        .service-item {
            border: 1px solid #ddd;
            padding: 15px;
            margin: 15px 0;
            border-radius: 5px;
            background: white;
        }
        .service-date {
            background: #0066cc;
            color: white;
            padding: 5px 10px;
            margin: -15px -15px 10px -15px;
            font-weight: bold;
        }
        .financial-summary {
            background: #e8f4f8;
            padding: 20px;
            border: 2px solid #0066cc;
            border-radius: 5px;
            margin: 20px 0;
        }
        .certification {
            text-align: center;
            margin: 30px 0;
            padding: 15px;
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 5px;
            font-style: italic;
        }
        .signature {
            text-align: center;
            margin-top: 40px;
        }
        @media print {
            body { margin: 0; padding: 10px; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>BON DE RÉSERVATION</h1>
            <h2>PARIS ELITE SERVICES</h2>
        </div>

        <div style="margin-bottom: 20px;">
            <p><strong>N° de Réservation:</strong> ${data.numeroReservation || 'N/A'}</p>
            <p><strong>Date d'émission:</strong> ${data.dateEmission || 'N/A'}</p>
        </div>

        <div class="info-section">
            <h3>📋 INFORMATIONS CLIENT</h3>
            <div class="info-row"><strong>Nom:</strong> ${data.nomClient || 'N/A'} ${data.prenomClient || ''}</div>
            <div class="info-row"><strong>Nationalité:</strong> ${data.nationalite || 'N/A'}</div>
            <div class="info-row"><strong>Nombre de passagers:</strong> ${data.nombrePassagers || 'N/A'} personne(s)</div>
            <div class="info-row"><strong>Téléphone:</strong> ${data.telephone || 'N/A'}</div>
            <div class="info-row"><strong>Email:</strong> ${data.email || 'N/A'}</div>
        </div>

        <div class="info-section">
            <h3>📍 PRESTATIONS RÉSERVÉES</h3>
            ${data.services && data.services.length > 0 ? data.services.map(service => `
                <div class="service-item">
                    <div class="service-date">${service.date || 'N/A'} - ${service.jour || ''}</div>
                    <p><strong>${service.heure || 'N/A'}</strong> - <strong>${service.type || 'N/A'}</strong></p>
                    <p>${service.description || 'Aucune description'}</p>
                    <p><strong>Lieu de prise en charge:</strong> ${service.lieuPriseEnCharge || 'N/A'}</p>
                    <p><strong>Destination:</strong> ${service.destination || 'N/A'}</p>
                    <p><strong>Durée:</strong> ${service.duree || 'N/A'}</p>
                    <p><strong>Tarif:</strong> ${service.tarif || '0'} €</p>
                </div>
            `).join('') : '<p>Aucun service ajouté</p>'}
        </div>

        <div class="financial-summary">
            <h3>💰 RÉCAPITULATIF FINANCIER</h3>
            <p><strong>Montant total des prestations:</strong> ${data.montantTotal || '0'} €</p>
            <p><strong>Acompte versé:</strong> ${data.acompte || '0'} €</p>
            <p><strong>Solde à régler:</strong> ${data.solde || '0'} €</p>
            <p><strong>Mode de paiement:</strong> ${data.modePaiement || 'Non spécifié'}</p>
        </div>

        <div class="info-section">
            <h3>📞 CONTACT URGENCE</h3>
            <p><strong>PARIS ELITE SERVICES</strong><br>
            Boris Porras del Castillo</p>
            <p><strong>Téléphone 24h/24:</strong> +33 6 68 25 11 02<br>
            <strong>Email:</strong> <EMAIL></p>
            <p><strong>Adresse:</strong> 11 allée Dumont d'Urville, 77200 TORCY<br>
            <strong>SIRET:</strong> 507 650 331 00022</p>
        </div>

        <div class="certification">
            <strong>Ce document certifie la réservation effective des prestations mentionnées ci-dessus et peut être présenté aux autorités en cas de contrôle.</strong>
        </div>

        <div class="signature">
            <p><strong>Date et signature:</strong><br>
            Paris, le ${data.dateSignature || new Date().toLocaleDateString('fr-FR')}</p>
            <br>
            <p><strong>PARIS ELITE SERVICES</strong><br>
            Boris Porras del Castillo<br>
            <em>Gérant</em></p>
        </div>
    </div>
</body>
</html>`;
        }

        function formatDate(dateString) {
            if (!dateString) return '';
            const date = new Date(dateString);
            return date.toLocaleDateString('fr-FR');
        }

        function getDayName(dateString) {
            if (!dateString) return '';
            const date = new Date(dateString);
            const days = ['Dimanche', 'Lundi', 'Mardi', 'Mercredi', 'Jeudi', 'Vendredi', 'Samedi'];
            return days[date.getDay()];
        }

        function generatePDF() {
            // Primero generar el aperçu si no existe
            if (document.getElementById('previewSection').style.display === 'none') {
                generatePreview();
                setTimeout(() => {
                    printDocument();
                }, 1000);
            } else {
                printDocument();
            }
        }

        function printDocument() {
            const iframe = document.getElementById('preview');
            iframe.contentWindow.print();
        }

        function fillExampleData() {
            // Remplir les données client
            document.getElementById('nomClient').value = 'GARCIA';
            document.getElementById('prenomClient').value = 'Maria';
            document.getElementById('nationalite').value = 'Espagnole';
            document.getElementById('nombrePassagers').value = '2';
            document.getElementById('telephone').value = '+34 612 345 678';
            document.getElementById('email').value = '<EMAIL>';

            // Remplir les données financières
            document.getElementById('montantTotal').value = '280';
            document.getElementById('acompte').value = '100';
            document.getElementById('modePaiement').value = 'Virement bancaire';

            // Calculer le solde
            calculateSolde();

            // Remplir le premier service s'il existe
            if (serviceCounter > 0) {
                const tomorrow = new Date();
                tomorrow.setDate(tomorrow.getDate() + 1);

                document.querySelector(`input[name="service_date_1"]`).value = tomorrow.toISOString().split('T')[0];
                document.querySelector(`input[name="service_heure_1"]`).value = '10:00';
                document.querySelector(`select[name="service_type_1"]`).value = 'Transfert aéroport';
                document.querySelector(`input[name="service_tarif_1"]`).value = '140';
                document.querySelector(`textarea[name="service_description_1"]`).value = 'Transfert depuis l\'aéroport CDG Terminal 2E vers l\'hôtel';
                document.querySelector(`input[name="service_lieu_1"]`).value = 'Aéroport CDG Terminal 2E';
                document.querySelector(`input[name="service_destination_1"]`).value = 'Hôtel Napoléon, Avenue de Friedland, Paris';
                document.querySelector(`input[name="service_duree_1"]`).value = '1 heure';
            }

            alert('Données d\'exemple remplies! Vous pouvez maintenant générer l\'aperçu.');
        }

        function resetForm() {
            if (confirm('Êtes-vous sûr de vouloir réinitialiser le formulaire ?')) {
                document.getElementById('reservationForm').reset();
                document.getElementById('servicesContainer').innerHTML = '';
                serviceCounter = 0;
                addService();
                document.getElementById('previewSection').style.display = 'none';
                // Regenerate reservation number and date
                document.getElementById('numeroReservation').value = generateReservationNumber();
                document.getElementById('dateEmission').value = new Date().toISOString().split('T')[0];
            }
        }
    </script>
</body>
</html>
