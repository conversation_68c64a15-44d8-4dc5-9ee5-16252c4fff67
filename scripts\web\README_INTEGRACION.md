# Integración del Módulo Bon de Réservation en Hostinger

## Descripción
Este módulo permite generar documentos oficiales de reserva en francés para presentar en controles fronterizos en París. Los clientes pueden completar un formulario y obtener un documento PDF profesional que certifica sus servicios reservados.

## Archivos Incluidos

### 1. Plantillas
- `plantillas/bon_reservation.md` - Plantilla base en Markdown
- `plantillas/bon_reservation.html` - Plantilla HTML con estilos

### 2. Generador Web
- `scripts/web/generador_bon_reservation.html` - Formulario completo para generar documentos
- `scripts/web/bon_reservation_api.php` - API PHP para procesamiento backend

## Instalación en Hostinger

### Paso 1: Subir Archivos
1. Accede a tu panel de Hostinger
2. Ve a "Administrador de Archivos" o usa FTP
3. Crea una carpeta llamada `bon-reservation` en tu directorio público
4. Sube los siguientes archivos:
   ```
   /public_html/bon-reservation/
   ├── index.html (renombrar generador_bon_reservation.html)
   ├── api.php (renombrar bon_reservation_api.php)
   ├── templates/
   │   └── bon_reservation_template.html
   └── generated_documents/ (crear carpeta vacía)
   ```

### Paso 2: Configurar Permisos
1. Asegúrate de que la carpeta `generated_documents/` tenga permisos de escritura (755 o 777)
2. Verifica que PHP esté habilitado en tu hosting

### Paso 3: Configurar el Dominio
- El módulo estará disponible en: `https://tudominio.com/bon-reservation/`

## Integración en tu Sitio Existente

### Opción 1: Página Independiente
Simplemente añade un enlace en tu menú principal:
```html
<a href="/bon-reservation/">Générer Bon de Réservation</a>
```

### Opción 2: Integración en WordPress
Si usas WordPress, puedes:
1. Crear una página nueva
2. Usar un shortcode personalizado
3. Embeber el formulario usando iframe

### Opción 3: Widget/Modal
Integrar el formulario como un modal popup en tu sitio existente.

## Personalización

### Modificar Estilos
Edita el CSS en `index.html` para que coincida con el diseño de tu sitio:
```css
/* Cambiar colores principales */
.btn-primary { background-color: #tu-color; }
.header h1 { color: #tu-color; }
```

### Añadir tu Logo
1. Sube tu logo a la carpeta `templates/`
2. Modifica la plantilla HTML para incluir:
```html
<img src="templates/tu-logo.png" alt="Logo" style="max-width: 200px;">
```

### Campos Adicionales
Para añadir campos al formulario:
1. Modifica `index.html` añadiendo el campo
2. Actualiza `api.php` para procesar el nuevo campo
3. Modifica la plantilla para mostrar el nuevo dato

## Funcionalidades

### Características Principales
- ✅ Formulario intuitivo en francés
- ✅ Validación de datos en tiempo real
- ✅ Generación de documentos HTML y PDF
- ✅ Descarga automática de archivos
- ✅ Diseño responsive (móvil y desktop)
- ✅ Cálculo automático de saldos
- ✅ Múltiples servicios por reserva

### Tipos de Servicios Soportados
- Transferts aéroport/gare
- Tours de ville
- Visites guidées
- Mise à disposition
- Services personnalisés

## Seguridad

### Medidas Implementadas
- Validación y sanitización de datos
- Protección contra XSS
- Validación de tipos de archivo
- Límites de tamaño de archivo

### Recomendaciones Adicionales
1. Configura HTTPS en tu dominio
2. Implementa límites de velocidad (rate limiting)
3. Considera añadir CAPTCHA para prevenir spam
4. Realiza backups regulares de los documentos generados

## Mantenimiento

### Limpieza de Archivos
Los documentos generados se acumulan en `generated_documents/`. Considera:
1. Implementar limpieza automática de archivos antiguos
2. Crear un script de mantenimiento mensual
3. Monitorear el espacio en disco

### Logs y Monitoreo
- Revisa los logs de PHP para errores
- Monitorea el uso del módulo
- Verifica que los PDFs se generen correctamente

## Soporte y Personalización

### Modificaciones Comunes

#### Cambiar Información de la Empresa
Edita las constantes en `api.php`:
```php
define('COMPANY_NAME', 'Tu Empresa');
define('COMPANY_ADDRESS', 'Tu Dirección');
define('COMPANY_PHONE', 'Tu Teléfono');
```

#### Añadir Idiomas
Para soporte multiidioma:
1. Crea plantillas en otros idiomas
2. Modifica el formulario para seleccionar idioma
3. Actualiza la API para usar la plantilla correcta

#### Integrar con Base de Datos
Para guardar las reservas:
1. Crea una tabla MySQL
2. Modifica `api.php` para guardar datos
3. Añade funcionalidad de consulta de reservas

## Resolución de Problemas

### Errores Comunes

#### "Bibliothèque PDF non disponible"
- Instala mPDF via Composer o usa una alternativa
- Contacta a Hostinger para verificar extensiones PHP

#### "Permisos insuficientes"
- Verifica permisos de la carpeta `generated_documents/`
- Contacta soporte de Hostinger si persiste

#### "Formulario no envía datos"
- Verifica que PHP esté habilitado
- Revisa los logs de error de PHP
- Confirma que la ruta de la API sea correcta

### Contacto para Soporte
Para modificaciones avanzadas o problemas técnicos, contacta al desarrollador del módulo.

## Próximas Mejoras

### Funcionalidades Planificadas
- [ ] Integración con sistema de pagos
- [ ] Notificaciones por email automáticas
- [ ] Panel de administración para gestionar reservas
- [ ] API REST para integraciones externas
- [ ] Firma digital de documentos
- [ ] Códigos QR para verificación

---

**Nota:** Este módulo está diseñado específicamente para cumplir con los requisitos de documentación para controles fronterizos en Francia. Asegúrate de mantener actualizada la información legal y de contacto.
