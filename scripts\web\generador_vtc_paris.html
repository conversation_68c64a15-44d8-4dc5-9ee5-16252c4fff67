<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Générateur de Réservation VTC - Paris Elite Services</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 900px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50, #34495e);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .header p {
            opacity: 0.9;
            font-size: 1.1rem;
        }

        .form-section {
            padding: 40px;
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin-bottom: 30px;
        }

        .form-group {
            position: relative;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #2c3e50;
            font-size: 0.95rem;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 15px;
            border: 2px solid #e0e6ed;
            border-radius: 10px;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #667eea;
            background: white;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .btn-section {
            text-align: center;
            margin-top: 30px;
        }

        .btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 15px 40px;
            border-radius: 50px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        .btn-secondary {
            background: linear-gradient(135deg, #28a745, #20c997);
        }

        .document {
            background: white;
            padding: 40px;
            margin: 20px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            font-family: 'Times New Roman', serif;
            line-height: 1.6;
            display: none;
        }

        .document.show {
            display: block;
        }

        .document-header {
            text-align: center;
            margin-bottom: 40px;
            border-bottom: 3px solid #2c3e50;
            padding-bottom: 20px;
        }

        .document-title {
            font-size: 2rem;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 10px;
        }

        .document-subtitle {
            font-size: 1.2rem;
            color: #666;
        }

        .info-section {
            margin: 25px 0;
            padding: 20px;
            background: #f8f9fa;
            border-left: 5px solid #667eea;
            border-radius: 5px;
        }

        .info-row {
            display: flex;
            justify-content: space-between;
            margin: 10px 0;
            padding: 5px 0;
        }

        .info-label {
            font-weight: bold;
            color: #2c3e50;
            min-width: 150px;
        }

        .info-value {
            color: #333;
            flex-grow: 1;
            text-align: right;
        }

        .signature-section {
            margin-top: 50px;
            display: flex;
            justify-content: space-between;
        }

        .signature-box {
            text-align: center;
            width: 200px;
        }

        .signature-line {
            border-top: 2px solid #333;
            margin-top: 40px;
            padding-top: 10px;
        }

        @media print {
            body {
                background: white;
                padding: 0;
            }
            
            .container {
                box-shadow: none;
                border-radius: 0;
            }
            
            .form-section,
            .btn-section {
                display: none;
            }
            
            .document {
                margin: 0;
                box-shadow: none;
                display: block !important;
            }
        }

        @media (max-width: 768px) {
            .form-grid {
                grid-template-columns: 1fr;
            }
            
            .signature-section {
                flex-direction: column;
                gap: 30px;
            }
            
            .header h1 {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚗 PARIS ELITE SERVICES</h1>
            <p>Document de réservation officiel pour contrôles fronterizos</p>
        </div>

        <div class="form-section">
            <form id="reservationForm">
                <div class="form-grid">
                    <div class="form-group">
                        <label for="companyName">Nom de l'entreprise *</label>
                        <input type="text" id="companyName" required value="PARIS ELITE SERVICES">
                    </div>

                    <div class="form-group">
                        <label for="licenseNumber">SIRET *</label>
                        <input type="text" id="licenseNumber" required value="*********** 00022">
                    </div>

                    <div class="form-group">
                        <label for="driverName">Nom du chauffeur *</label>
                        <input type="text" id="driverName" required value="Boris Porras del Castillo">
                    </div>

                    <div class="form-group">
                        <label for="vehiclePlate">Plaque d'immatriculation *</label>
                        <input type="text" id="vehiclePlate" required placeholder="AB-123-CD" style="text-transform: uppercase;">
                    </div>

                    <div class="form-group">
                        <label for="clientName">Nom du client *</label>
                        <input type="text" id="clientName" required placeholder="Nom du passager">
                    </div>

                    <div class="form-group">
                        <label for="clientPhone">Téléphone du client *</label>
                        <input type="tel" id="clientPhone" required placeholder="06 12 34 56 78">
                    </div>

                    <div class="form-group">
                        <label for="pickupAddress">Adresse de prise en charge *</label>
                        <input type="text" id="pickupAddress" required placeholder="Aéroport CDG Terminal 2E">
                    </div>

                    <div class="form-group">
                        <label for="destinationAddress">Adresse de destination *</label>
                        <input type="text" id="destinationAddress" required placeholder="Hôtel, adresse Paris">
                    </div>

                    <div class="form-group">
                        <label for="reservationDate">Date de réservation *</label>
                        <input type="date" id="reservationDate" required>
                    </div>

                    <div class="form-group">
                        <label for="pickupTime">Heure de prise en charge *</label>
                        <input type="time" id="pickupTime" required>
                    </div>

                    <div class="form-group">
                        <label for="reservationNumber">Numéro de réservation</label>
                        <input type="text" id="reservationNumber" placeholder="Généré automatiquement">
                    </div>

                    <div class="form-group">
                        <label for="estimatedPrice">Prix estimé (€)</label>
                        <input type="number" id="estimatedPrice" placeholder="140" min="0" step="0.01">
                    </div>
                </div>

                <div class="form-group">
                    <label for="specialInstructions">Instructions particulières</label>
                    <textarea id="specialInstructions" rows="3" placeholder="Vol retardé, attente possible, bagages nombreux..."></textarea>
                </div>

                <div class="btn-section">
                    <button type="submit" class="btn">📄 Générer le Document</button>
                    <button type="button" class="btn btn-secondary" onclick="window.print()">🖨️ Imprimer</button>
                </div>
            </form>
        </div>

        <div id="document" class="document">
            <div class="document-header">
                <div class="document-title">DOCUMENT DE RÉSERVATION VTC</div>
                <div class="document-subtitle">PARIS ELITE SERVICES</div>
                <div class="document-subtitle">Valable pour contrôle des forces de l'ordre</div>
            </div>

            <div class="info-section">
                <h3 style="margin-bottom: 15px; color: #2c3e50;">📋 INFORMATIONS TRANSPORTEUR</h3>
                <div class="info-row">
                    <span class="info-label">Entreprise :</span>
                    <span class="info-value" id="doc-companyName"></span>
                </div>
                <div class="info-row">
                    <span class="info-label">SIRET :</span>
                    <span class="info-value" id="doc-licenseNumber"></span>
                </div>
                <div class="info-row">
                    <span class="info-label">Chauffeur :</span>
                    <span class="info-value" id="doc-driverName"></span>
                </div>
                <div class="info-row">
                    <span class="info-label">Véhicule :</span>
                    <span class="info-value" id="doc-vehiclePlate"></span>
                </div>
                <div class="info-row">
                    <span class="info-label">Téléphone :</span>
                    <span class="info-value">+33 6 68 25 11 02</span>
                </div>
            </div>

            <div class="info-section">
                <h3 style="margin-bottom: 15px; color: #2c3e50;">👤 INFORMATIONS CLIENT</h3>
                <div class="info-row">
                    <span class="info-label">Nom du client :</span>
                    <span class="info-value" id="doc-clientName"></span>
                </div>
                <div class="info-row">
                    <span class="info-label">Téléphone :</span>
                    <span class="info-value" id="doc-clientPhone"></span>
                </div>
                <div class="info-row">
                    <span class="info-label">N° de réservation :</span>
                    <span class="info-value" id="doc-reservationNumber"></span>
                </div>
            </div>

            <div class="info-section">
                <h3 style="margin-bottom: 15px; color: #2c3e50;">📍 DÉTAILS DE LA COURSE</h3>
                <div class="info-row">
                    <span class="info-label">Date :</span>
                    <span class="info-value" id="doc-reservationDate"></span>
                </div>
                <div class="info-row">
                    <span class="info-label">Heure de RDV :</span>
                    <span class="info-value" id="doc-pickupTime"></span>
                </div>
                <div class="info-row">
                    <span class="info-label">Prise en charge :</span>
                    <span class="info-value" id="doc-pickupAddress"></span>
                </div>
                <div class="info-row">
                    <span class="info-label">Destination :</span>
                    <span class="info-value" id="doc-destinationAddress"></span>
                </div>
                <div class="info-row" id="priceRow" style="display: none;">
                    <span class="info-label">Prix estimé :</span>
                    <span class="info-value" id="doc-estimatedPrice"></span>
                </div>
                <div id="instructionsRow" style="display: none;">
                    <div style="margin-top: 15px;">
                        <span class="info-label">Instructions :</span>
                        <div style="margin-top: 5px; font-style: italic;" id="doc-specialInstructions"></div>
                    </div>
                </div>
            </div>

            <div style="margin-top: 30px; padding: 20px; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px;">
                <p style="margin: 0; font-weight: bold; color: #856404;">
                    ⚖️ MENTIONS LÉGALES : Ce document certifie la réservation effective des prestations 
                    de transport touristique. Conforme aux réglementations françaises pour les contrôles 
                    fronterizos. SIRET: *********** 00022
                </p>
            </div>

            <div class="signature-section">
                <div class="signature-box">
                    <div>Le chauffeur</div>
                    <div class="signature-line">Boris Porras del Castillo</div>
                </div>
                <div style="text-align: center; padding-top: 20px;">
                    <div style="font-weight: bold;">Document généré le :</div>
                    <div id="generationDate"></div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Génération automatique du numéro de réservation
        function generateReservationNumber() {
            const date = new Date();
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            const random = Math.floor(Math.random() * 9999).toString().padStart(4, '0');
            return `PES${year}${month}${day}${random}`;
        }

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            // Date par défaut = aujourd'hui
            const today = new Date().toISOString().split('T')[0];
            document.getElementById('reservationDate').value = today;
            
            // Numéro de réservation automatique
            document.getElementById('reservationNumber').value = generateReservationNumber();
            
            // Format plaque d'immatriculation
            document.getElementById('vehiclePlate').addEventListener('input', function(e) {
                e.target.value = e.target.value.toUpperCase();
            });
        });

        // Gestion du formulaire
        document.getElementById('reservationForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Récupération des valeurs
            const formData = new FormData(e.target);
            const data = Object.fromEntries(formData.entries());
            
            // Validation
            const requiredFields = ['companyName', 'licenseNumber', 'driverName', 'vehiclePlate', 
                                  'clientName', 'clientPhone', 'pickupAddress', 'destinationAddress', 
                                  'reservationDate', 'pickupTime'];
            
            for (let field of requiredFields) {
                if (!data[field]) {
                    alert('Veuillez remplir tous les champs obligatoires (*)');
                    return;
                }
            }
            
            // Génération automatique si vide
            if (!data.reservationNumber) {
                data.reservationNumber = generateReservationNumber();
            }
            
            // Remplissage du document
            document.getElementById('doc-companyName').textContent = data.companyName;
            document.getElementById('doc-licenseNumber').textContent = data.licenseNumber;
            document.getElementById('doc-driverName').textContent = data.driverName;
            document.getElementById('doc-vehiclePlate').textContent = data.vehiclePlate;
            document.getElementById('doc-clientName').textContent = data.clientName;
            document.getElementById('doc-clientPhone').textContent = data.clientPhone;
            document.getElementById('doc-reservationNumber').textContent = data.reservationNumber;
            
            // Formatage de la date
            const dateObj = new Date(data.reservationDate);
            const options = { 
                weekday: 'long', 
                year: 'numeric', 
                month: 'long', 
                day: 'numeric' 
            };
            document.getElementById('doc-reservationDate').textContent = 
                dateObj.toLocaleDateString('fr-FR', options);
            
            document.getElementById('doc-pickupTime').textContent = data.pickupTime;
            document.getElementById('doc-pickupAddress').textContent = data.pickupAddress;
            document.getElementById('doc-destinationAddress').textContent = data.destinationAddress;
            
            // Prix (optionnel)
            if (data.estimatedPrice) {
                document.getElementById('doc-estimatedPrice').textContent = data.estimatedPrice + ' €';
                document.getElementById('priceRow').style.display = 'flex';
            } else {
                document.getElementById('priceRow').style.display = 'none';
            }
            
            // Instructions (optionnel)
            if (data.specialInstructions) {
                document.getElementById('doc-specialInstructions').textContent = data.specialInstructions;
                document.getElementById('instructionsRow').style.display = 'block';
            } else {
                document.getElementById('instructionsRow').style.display = 'none';
            }
            
            // Date de génération
            document.getElementById('generationDate').textContent = 
                new Date().toLocaleString('fr-FR');
            
            // Affichage du document
            document.getElementById('document').classList.add('show');
            
            // Scroll vers le document
            document.getElementById('document').scrollIntoView({ 
                behavior: 'smooth' 
            });
        });
    </script>
</body>
</html>
