<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Info - Générateurs</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            color: #0066cc;
        }
        
        .debug-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #f8f9fa;
        }
        
        .debug-section h3 {
            color: #2c3e50;
            margin-bottom: 15px;
        }
        
        .btn {
            background: #0066cc;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        
        .btn:hover {
            background: #0052a3;
        }
        
        .log {
            background: #000;
            color: #00ff00;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin-top: 10px;
        }
        
        .error {
            color: #ff0000;
        }
        
        .success {
            color: #00ff00;
        }
        
        .warning {
            color: #ffff00;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 Debug Info - Générateurs</h1>
            <p>Diagnostic des problèmes avec les boutons</p>
        </div>

        <div class="debug-section">
            <h3>🧪 Tests de Base</h3>
            <button class="btn" onclick="testBasicJS()">Test JavaScript</button>
            <button class="btn" onclick="testConsole()">Test Console</button>
            <button class="btn" onclick="testAlert()">Test Alert</button>
            <button class="btn" onclick="clearLog()">Effacer Log</button>
        </div>

        <div class="debug-section">
            <h3>📋 Tests des Générateurs</h3>
            <button class="btn" onclick="testVTCGenerator()">Test VTC Generator</button>
            <button class="btn" onclick="testBonReservation()">Test Bon Réservation</button>
            <button class="btn" onclick="testSimpleVersion()">Test Version Simple</button>
        </div>

        <div class="debug-section">
            <h3>🔍 Diagnostic des Erreurs</h3>
            <button class="btn" onclick="checkErrors()">Vérifier Erreurs</button>
            <button class="btn" onclick="checkFunctions()">Vérifier Fonctions</button>
            <button class="btn" onclick="checkElements()">Vérifier Éléments</button>
        </div>

        <div class="debug-section">
            <h3>📊 Console de Debug</h3>
            <div id="debugLog" class="log">
                Console de debug initialisée...<br>
            </div>
        </div>

        <div class="debug-section">
            <h3>📝 Instructions de Debug</h3>
            <ol>
                <li><strong>Ouvrir la Console du Navigateur:</strong> F12 → Console</li>
                <li><strong>Tester les boutons ci-dessus</strong> et vérifier les messages</li>
                <li><strong>Si les boutons ne fonctionnent pas:</strong> Vérifier les erreurs JavaScript</li>
                <li><strong>Problèmes courants:</strong>
                    <ul>
                        <li>Erreurs de syntaxe JavaScript</li>
                        <li>Fonctions non définies</li>
                        <li>Éléments DOM non trouvés</li>
                        <li>Conflits entre scripts</li>
                    </ul>
                </li>
            </ol>
        </div>
    </div>

    <script>
        function log(message, type = 'info') {
            const logDiv = document.getElementById('debugLog');
            const timestamp = new Date().toLocaleTimeString();
            let className = '';
            
            switch(type) {
                case 'error': className = 'error'; break;
                case 'success': className = 'success'; break;
                case 'warning': className = 'warning'; break;
            }
            
            logDiv.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span><br>`;
            logDiv.scrollTop = logDiv.scrollHeight;
            
            // Aussi dans la console du navigateur
            console.log(`[DEBUG] ${message}`);
        }

        function testBasicJS() {
            log('Test JavaScript de base...', 'info');
            try {
                const test = 1 + 1;
                log(`✅ JavaScript fonctionne: 1 + 1 = ${test}`, 'success');
            } catch (e) {
                log(`❌ Erreur JavaScript: ${e.message}`, 'error');
            }
        }

        function testConsole() {
            log('Test de la console...', 'info');
            console.log('Test console.log');
            console.error('Test console.error');
            console.warn('Test console.warn');
            log('✅ Messages envoyés à la console du navigateur', 'success');
        }

        function testAlert() {
            log('Test des alertes...', 'info');
            alert('Test alert - Si vous voyez ceci, les alertes fonctionnent!');
            log('✅ Alert testé', 'success');
        }

        function clearLog() {
            document.getElementById('debugLog').innerHTML = 'Console effacée...<br>';
        }

        function testVTCGenerator() {
            log('Test du générateur VTC...', 'info');
            try {
                // Simuler l'ouverture du générateur VTC
                const vtcUrl = 'generador_vtc_paris.html';
                log(`Tentative d'ouverture: ${vtcUrl}`, 'info');
                
                // Vérifier si le fichier existe
                fetch(vtcUrl)
                    .then(response => {
                        if (response.ok) {
                            log('✅ Générateur VTC accessible', 'success');
                            window.open(vtcUrl, '_blank');
                        } else {
                            log(`❌ Générateur VTC non accessible (${response.status})`, 'error');
                        }
                    })
                    .catch(error => {
                        log(`❌ Erreur d'accès au générateur VTC: ${error.message}`, 'error');
                    });
            } catch (e) {
                log(`❌ Erreur lors du test VTC: ${e.message}`, 'error');
            }
        }

        function testBonReservation() {
            log('Test du générateur Bon de Réservation...', 'info');
            try {
                const bonUrl = 'generador_bon_reservation.html';
                log(`Tentative d'ouverture: ${bonUrl}`, 'info');
                
                fetch(bonUrl)
                    .then(response => {
                        if (response.ok) {
                            log('✅ Générateur Bon de Réservation accessible', 'success');
                            window.open(bonUrl, '_blank');
                        } else {
                            log(`❌ Générateur Bon de Réservation non accessible (${response.status})`, 'error');
                        }
                    })
                    .catch(error => {
                        log(`❌ Erreur d'accès au générateur Bon de Réservation: ${error.message}`, 'error');
                    });
            } catch (e) {
                log(`❌ Erreur lors du test Bon de Réservation: ${e.message}`, 'error');
            }
        }

        function testSimpleVersion() {
            log('Test de la version simple...', 'info');
            try {
                const simpleUrl = 'test_simple.html';
                log(`Tentative d'ouverture: ${simpleUrl}`, 'info');
                
                fetch(simpleUrl)
                    .then(response => {
                        if (response.ok) {
                            log('✅ Version simple accessible', 'success');
                            window.open(simpleUrl, '_blank');
                        } else {
                            log(`❌ Version simple non accessible (${response.status})`, 'error');
                        }
                    })
                    .catch(error => {
                        log(`❌ Erreur d'accès à la version simple: ${error.message}`, 'error');
                    });
            } catch (e) {
                log(`❌ Erreur lors du test version simple: ${e.message}`, 'error');
            }
        }

        function checkErrors() {
            log('Vérification des erreurs JavaScript...', 'info');
            
            // Capturer les erreurs JavaScript
            window.addEventListener('error', function(e) {
                log(`❌ Erreur JavaScript détectée: ${e.message} (ligne ${e.lineno})`, 'error');
            });
            
            log('✅ Écouteur d\'erreurs activé', 'success');
        }

        function checkFunctions() {
            log('Vérification des fonctions disponibles...', 'info');
            
            const functions = ['generatePreview', 'generatePDF', 'fillExampleData', 'resetForm'];
            
            functions.forEach(funcName => {
                if (typeof window[funcName] === 'function') {
                    log(`✅ Fonction ${funcName} disponible`, 'success');
                } else {
                    log(`❌ Fonction ${funcName} non disponible`, 'error');
                }
            });
        }

        function checkElements() {
            log('Vérification des éléments DOM...', 'info');
            
            const elements = ['reservationForm', 'previewSection', 'preview'];
            
            elements.forEach(elemId => {
                const elem = document.getElementById(elemId);
                if (elem) {
                    log(`✅ Élément ${elemId} trouvé`, 'success');
                } else {
                    log(`❌ Élément ${elemId} non trouvé`, 'error');
                }
            });
        }

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            log('🚀 Page de debug chargée', 'success');
            log('Utilisez les boutons ci-dessus pour diagnostiquer les problèmes', 'info');
            
            // Activer la capture d'erreurs
            checkErrors();
        });

        // Capturer les erreurs non gérées
        window.addEventListener('unhandledrejection', function(e) {
            log(`❌ Promise rejetée: ${e.reason}`, 'error');
        });
    </script>
</body>
</html>
