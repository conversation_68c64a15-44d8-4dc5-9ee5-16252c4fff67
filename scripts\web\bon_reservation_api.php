<?php
/**
 * API para generar Bon de Réservation - Paris Elite Services
 * Para integrar en sitio web Hostinger
 */

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Configuración
define('UPLOAD_DIR', 'generated_documents/');
define('TEMPLATE_DIR', 'templates/');

// Crear directorio si no existe
if (!file_exists(UPLOAD_DIR)) {
    mkdir(UPLOAD_DIR, 0755, true);
}

// Manejar solicitudes OPTIONS (CORS)
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Función principal
switch ($_SERVER['REQUEST_METHOD']) {
    case 'POST':
        if (isset($_POST['action'])) {
            switch ($_POST['action']) {
                case 'generate_html':
                    generateHTML();
                    break;
                case 'generate_pdf':
                    generatePDF();
                    break;
                default:
                    sendError('Action non reconnue');
            }
        } else {
            sendError('Action manquante');
        }
        break;
    case 'GET':
        if (isset($_GET['file'])) {
            downloadFile($_GET['file']);
        } else {
            sendError('Fichier non spécifié');
        }
        break;
    default:
        sendError('Méthode non autorisée');
}

function generateHTML() {
    try {
        $data = validateAndSanitizeData($_POST);
        $html = generateHTMLDocument($data);
        
        $filename = 'bon_reservation_' . $data['numeroReservation'] . '_' . date('Y-m-d') . '.html';
        $filepath = UPLOAD_DIR . $filename;
        
        file_put_contents($filepath, $html);
        
        sendSuccess([
            'message' => 'Document HTML généré avec succès',
            'filename' => $filename,
            'download_url' => $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . '/' . $filepath
        ]);
        
    } catch (Exception $e) {
        sendError('Erreur lors de la génération: ' . $e->getMessage());
    }
}

function generatePDF() {
    try {
        $data = validateAndSanitizeData($_POST);
        
        // Vérifier si mPDF est disponible
        if (!class_exists('Mpdf\Mpdf')) {
            // Alternative: utiliser DomPDF ou TCPDF
            sendError('Bibliothèque PDF non disponible. Contactez l\'administrateur.');
            return;
        }
        
        $html = generateHTMLDocument($data);
        
        $mpdf = new \Mpdf\Mpdf([
            'mode' => 'utf-8',
            'format' => 'A4',
            'margin_left' => 15,
            'margin_right' => 15,
            'margin_top' => 16,
            'margin_bottom' => 16,
        ]);
        
        $mpdf->WriteHTML($html);
        
        $filename = 'bon_reservation_' . $data['numeroReservation'] . '_' . date('Y-m-d') . '.pdf';
        $filepath = UPLOAD_DIR . $filename;
        
        $mpdf->Output($filepath, 'F');
        
        sendSuccess([
            'message' => 'Document PDF généré avec succès',
            'filename' => $filename,
            'download_url' => $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . '/' . $filepath
        ]);
        
    } catch (Exception $e) {
        sendError('Erreur lors de la génération PDF: ' . $e->getMessage());
    }
}

function validateAndSanitizeData($post) {
    $required_fields = [
        'numeroReservation', 'dateEmission', 'nomClient', 'prenomClient',
        'nationalite', 'nombrePassagers', 'telephone', 'email',
        'montantTotal', 'modePaiement'
    ];
    
    $data = [];
    
    // Valider les champs requis
    foreach ($required_fields as $field) {
        if (!isset($post[$field]) || empty(trim($post[$field]))) {
            throw new Exception("Champ requis manquant: $field");
        }
        $data[$field] = htmlspecialchars(trim($post[$field]), ENT_QUOTES, 'UTF-8');
    }
    
    // Valider l'email
    if (!filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
        throw new Exception("Format d'email invalide");
    }
    
    // Valider les montants
    if (!is_numeric($data['montantTotal']) || $data['montantTotal'] < 0) {
        throw new Exception("Montant total invalide");
    }
    
    // Traiter les champs optionnels
    $data['acompte'] = isset($post['acompte']) ? floatval($post['acompte']) : 0;
    $data['solde'] = $data['montantTotal'] - $data['acompte'];
    $data['dateSignature'] = date('d/m/Y');
    
    // Traiter les services
    $data['services'] = [];
    $serviceIndex = 1;
    
    while (isset($post["service_date_$serviceIndex"])) {
        $service = [
            'date' => formatDate($post["service_date_$serviceIndex"]),
            'jour' => getDayName($post["service_date_$serviceIndex"]),
            'heure' => htmlspecialchars($post["service_heure_$serviceIndex"], ENT_QUOTES, 'UTF-8'),
            'type' => htmlspecialchars($post["service_type_$serviceIndex"], ENT_QUOTES, 'UTF-8'),
            'description' => htmlspecialchars($post["service_description_$serviceIndex"] ?? '', ENT_QUOTES, 'UTF-8'),
            'lieuPriseEnCharge' => htmlspecialchars($post["service_lieu_$serviceIndex"], ENT_QUOTES, 'UTF-8'),
            'destination' => htmlspecialchars($post["service_destination_$serviceIndex"], ENT_QUOTES, 'UTF-8'),
            'duree' => htmlspecialchars($post["service_duree_$serviceIndex"], ENT_QUOTES, 'UTF-8'),
            'tarif' => floatval($post["service_tarif_$serviceIndex"])
        ];
        
        $data['services'][] = $service;
        $serviceIndex++;
    }
    
    return $data;
}

function generateHTMLDocument($data) {
    $template = file_get_contents(TEMPLATE_DIR . 'bon_reservation_template.html');
    
    if (!$template) {
        // Template inline si le fichier n'existe pas
        $template = getInlineTemplate();
    }
    
    // Remplacer les placeholders
    $replacements = [
        '[NUMERO_RESERVATION]' => $data['numeroReservation'],
        '[DATE_EMISSION]' => formatDate($data['dateEmission']),
        '[NOM_CLIENT]' => $data['nomClient'],
        '[PRENOM_CLIENT]' => $data['prenomClient'],
        '[NATIONALITE]' => $data['nationalite'],
        '[NOMBRE_PASSAGERS]' => $data['nombrePassagers'],
        '[TELEPHONE]' => $data['telephone'],
        '[EMAIL]' => $data['email'],
        '[MONTANT_TOTAL]' => number_format($data['montantTotal'], 2),
        '[ACOMPTE]' => number_format($data['acompte'], 2),
        '[SOLDE]' => number_format($data['solde'], 2),
        '[MODE_PAIEMENT]' => $data['modePaiement'],
        '[DATE_SIGNATURE]' => $data['dateSignature']
    ];
    
    // Générer les services
    $servicesHTML = '';
    foreach ($data['services'] as $service) {
        $servicesHTML .= generateServiceHTML($service);
    }
    $replacements['[SERVICES_CONTENT]'] = $servicesHTML;
    
    // Appliquer les remplacements
    foreach ($replacements as $placeholder => $value) {
        $template = str_replace($placeholder, $value, $template);
    }
    
    return $template;
}

function generateServiceHTML($service) {
    return "
        <div class='service-item'>
            <div class='service-date'>{$service['date']} - {$service['jour']}</div>
            <div class='service-details'>
                <p><strong>{$service['heure']}</strong> - <strong>{$service['type']}</strong></p>
                <p>{$service['description']}</p>
                <p><strong>Lieu de prise en charge:</strong> {$service['lieuPriseEnCharge']}</p>
                <p><strong>Destination:</strong> {$service['destination']}</p>
                <p><strong>Durée:</strong> {$service['duree']}</p>
                <p><strong>Tarif:</strong> " . number_format($service['tarif'], 2) . " €</p>
            </div>
        </div>
    ";
}

function formatDate($dateString) {
    $date = new DateTime($dateString);
    return $date->format('d/m/Y');
}

function getDayName($dateString) {
    $date = new DateTime($dateString);
    $days = ['Dimanche', 'Lundi', 'Mardi', 'Mercredi', 'Jeudi', 'Vendredi', 'Samedi'];
    return $days[$date->format('w')];
}

function downloadFile($filename) {
    $filepath = UPLOAD_DIR . basename($filename);
    
    if (!file_exists($filepath)) {
        sendError('Fichier non trouvé');
        return;
    }
    
    $extension = pathinfo($filepath, PATHINFO_EXTENSION);
    $contentType = $extension === 'pdf' ? 'application/pdf' : 'text/html';
    
    header('Content-Type: ' . $contentType);
    header('Content-Disposition: attachment; filename="' . basename($filename) . '"');
    header('Content-Length: ' . filesize($filepath));
    
    readfile($filepath);
    exit();
}

function sendSuccess($data) {
    echo json_encode(['success' => true, 'data' => $data]);
    exit();
}

function sendError($message) {
    http_response_code(400);
    echo json_encode(['success' => false, 'error' => $message]);
    exit();
}

function getInlineTemplate() {
    return '<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <title>Bon de Réservation - Paris Elite Services</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { text-align: center; color: #0066cc; margin-bottom: 30px; }
        .section { margin: 20px 0; }
        .service-item { border: 1px solid #ddd; padding: 15px; margin: 10px 0; }
        .service-date { background: #0066cc; color: white; padding: 5px; margin: -15px -15px 10px -15px; }
        .financial { background: #f0f8ff; padding: 15px; border: 2px solid #0066cc; }
        .footer { text-align: center; margin-top: 30px; }
    </style>
</head>
<body>
    <div class="header">
        <h1>BON DE RÉSERVATION</h1>
        <h2>PARIS ELITE SERVICES</h2>
    </div>
    
    <p><strong>N° de Réservation:</strong> [NUMERO_RESERVATION]</p>
    <p><strong>Date d\'émission:</strong> [DATE_EMISSION]</p>
    
    <div class="section">
        <h3>INFORMATIONS CLIENT</h3>
        <p><strong>Nom:</strong> [NOM_CLIENT] [PRENOM_CLIENT]</p>
        <p><strong>Nationalité:</strong> [NATIONALITE]</p>
        <p><strong>Nombre de passagers:</strong> [NOMBRE_PASSAGERS] personne(s)</p>
        <p><strong>Téléphone:</strong> [TELEPHONE]</p>
        <p><strong>Email:</strong> [EMAIL]</p>
    </div>
    
    <div class="section">
        <h3>PRESTATIONS RÉSERVÉES</h3>
        [SERVICES_CONTENT]
    </div>
    
    <div class="section">
        <h3>RÉCAPITULATIF FINANCIER</h3>
        <div class="financial">
            <p><strong>Montant total:</strong> [MONTANT_TOTAL] €</p>
            <p><strong>Acompte versé:</strong> [ACOMPTE] €</p>
            <p><strong>Solde à régler:</strong> [SOLDE] €</p>
            <p><strong>Mode de paiement:</strong> [MODE_PAIEMENT]</p>
        </div>
    </div>
    
    <div class="section">
        <h3>CONDITIONS GÉNÉRALES</h3>
        <ul>
            <li>Cette réservation est confirmée et garantie</li>
            <li>Annulation possible jusqu\'à 72h avant le service sans frais</li>
            <li>Véhicules assurés et chauffeurs professionnels</li>
            <li>Service disponible en français, espagnol et anglais</li>
        </ul>
    </div>
    
    <div class="section">
        <h3>CONTACT URGENCE</h3>
        <p><strong>PARIS ELITE SERVICES</strong><br>
        Boris Porras del Castillo<br>
        <strong>Téléphone 24h/24:</strong> +33 6 68 25 11 02<br>
        <strong>Email:</strong> <EMAIL><br>
        <strong>Adresse:</strong> 11 allée Dumont d\'Urville, 77200 TORCY<br>
        <strong>SIRET:</strong> 507 650 331 00022</p>
    </div>
    
    <div style="text-align: center; margin: 30px 0; padding: 15px; background: #d4edda; border: 1px solid #c3e6cb;">
        <em>Ce document certifie la réservation effective des prestations mentionnées ci-dessus et peut être présenté aux autorités en cas de contrôle.</em>
    </div>
    
    <div class="footer">
        <p><strong>Date et signature:</strong><br>
        Paris, le [DATE_SIGNATURE]</p>
        <br>
        <p><strong>PARIS ELITE SERVICES</strong><br>
        Boris Porras del Castillo<br>
        <em>Gérant</em></p>
    </div>
</body>
</html>';
}
?>
